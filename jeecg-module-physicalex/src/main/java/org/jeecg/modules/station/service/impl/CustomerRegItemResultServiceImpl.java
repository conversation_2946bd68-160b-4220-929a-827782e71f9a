package org.jeecg.modules.station.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mzlion.easyokhttp.HttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.common.system.vo.DictModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.GroovyResultException;
import org.jeecg.excommons.utils.GroovyUtil;
import org.jeecg.modules.basicinfo.entity.ItemDict;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.reg.dto.DependentItemResultDTO;
import org.jeecg.modules.basicinfo.mapper.ItemDictMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemInfoMapper;
import org.jeecg.modules.basicinfo.mapper.ItemStandardMapper;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.ISysUserInterfaceService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.sms.service.ISmsRecordsService;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegDepartSummaryMapper;
import org.jeecg.modules.station.mapper.CustomerRegItemResultMapper;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.bo.DepartAndGroupBean;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.HashMap;

/**
 * @Description: 体检小项结果表
 * @Author: jeecg-boot
 * @Date: 2024-04-21
 * @Version: V1.0
 */
@Service
public class CustomerRegItemResultServiceImpl extends ServiceImpl<CustomerRegItemResultMapper, CustomerRegItemResult> implements ICustomerRegItemResultService {
    @Autowired
    private CustomerRegItemResultMapper customerRegItemResultMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ItemStandardMapper itemStandardMapper;
    @Autowired
    private ISmsRecordsService smsRecordsService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SysDepartMapper sysDepartMapper;

    @Autowired
    private IItemGroupRelationService itemGroupRelationService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private ItemDictMapper itemDictMapper;
    @Autowired
    private CustomerRegDepartSummaryMapper customerRegDepartSummaryMapper;

    @Override
    public void fetchCheckData(String customerRegId, String startTime, String endTime) throws Exception {
        String interfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(interfaceUrl)) {
            throw new Exception("未配置接口地址");
        }
        //如果interfaceUrl最后一个字符是/，则去掉
        interfaceUrl = StringUtils.removeEnd(interfaceUrl, "/");
        String url = interfaceUrl + ExApiConstants.REPORT_CHECK_SYNC_PATH;
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isNotBlank(customerRegId)) {
            params.put("customerRegId", customerRegId);
        }
        if (StringUtils.isNotBlank(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            params.put("endTime", endTime);
        }

        //遍历params拼接参数
        StringBuilder paramStr = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            paramStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (!paramStr.isEmpty()) {
            paramStr.deleteCharAt(paramStr.length() - 1);
        }
        url = url + "?" + paramStr;
        try {
            //HttpClientUtil.sendGet(url);
            HttpClient.get(url).execute();
        } catch (Exception e) {
            log.error("从检查获取报告数据异常", e);
            throw new Exception("从检查获取报告数据异常！详细原因：" + e.getMessage());
        }
    }

    @Override
    public void fetchLisData(String customerRegId, String startTime, String endTime) throws Exception {

        String interfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        if (StringUtils.isBlank(interfaceUrl)) {
            throw new Exception("未配置接口地址");
        }
        //如果interfaceUrl最后一个字符是/，则去掉
        interfaceUrl = StringUtils.removeEnd(interfaceUrl, "/");


        String url = interfaceUrl + ExApiConstants.REPORT_LIS_SYNC_PATH;
        Map<String, String> params = new HashMap<>();
        if (StringUtils.isNotBlank(customerRegId)) {
            params.put("customerRegId", customerRegId);
        }
        if (StringUtils.isNotBlank(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            params.put("endTime", endTime);
        }

        //遍历params拼接参数
        StringBuilder paramStr = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            paramStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        if (!paramStr.isEmpty()) {
            paramStr.deleteCharAt(paramStr.length() - 1);
        }
        url = url + "?" + paramStr.toString();
        try {
            //HttpClientUtil.sendGet(url);
            HttpClient.get(url).execute();
        } catch (Exception e) {
            log.error("从lis同步数据异常", e);
            throw new Exception("调用接口异常");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<CustomerRegItemResult> saveItemResult(List<CustomerRegItemResult> customerRegItemResultList, CustomerReg reg, Boolean isTempSave) {
        if (customerRegItemResultList == null || customerRegItemResultList.isEmpty()) {
            return customerRegItemResultList;
        }

        Set<String> abnormalGroupIdSet = new HashSet<>();
        Set<String> groupIdSet = new HashSet<>();

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        for (CustomerRegItemResult itemResult : customerRegItemResultList) {
            itemResult.setExamNo(reg.getExamNo());
            itemResult.setCustomerRegId(reg.getId());
            itemResult.setArchivesNum(reg.getArchivesNum());
            groupIdSet.add(itemResult.getItemGroupId());
            itemResult.setDoctorId(sysUser.getUsername());
            itemResult.setDoctorName(sysUser.getRealname());

            if (!Objects.equals(itemResult.getAbandonFlag(),1) && StringUtils.isBlank(itemResult.getAbnormalFlag())) {
                String itemId = itemResult.getItemId();
                if (StringUtils.isNotBlank(itemId)) {
                    ItemStandard matchedStandard = findMatchedItemStandard(itemResult, reg);
                    if (matchedStandard != null) {
                        itemResult.setMatchedStandardId(matchedStandard.getId());
                        //设置危急值标志
                        String criticalDegree = matchedStandard.getSeverityDegree();
                        if (StringUtils.isNotBlank(criticalDegree)) {
                            itemResult.setCriticalFlag("1");
                            itemResult.setCriticalDegree(criticalDegree);
                        } else {
                            itemResult.setCriticalFlag(null);
                            itemResult.setCriticalDegree(null);
                        }
                        //设置异常标志
                        itemResult.setAbnormalFlagDesc(StringUtils.stripToEmpty(matchedStandard.getSymbo()));
                        itemResult.setAbnormalFlag(matchedStandard.getAbnormalFlag());
                        itemResult.setAbnormalSymbol(StringUtils.stripToEmpty(matchedStandard.getSymboChar()));

                        //设置结论
                        if (StringUtils.isNotBlank(matchedStandard.getConclusion())) {
                            itemResult.setCheckConclusion(StringUtils.stripToEmpty(matchedStandard.getConclusion()));
                        }
                        /*else {
                            if (StringUtils.equals(itemResult.getValueType(), ExConstants.ITEM_VALUE_TYPE_说明型)) {
                                itemResult.setCheckConclusion(itemResult.getValue());
                            }
                        }*/
                    } else {
                        if (StringUtils.isNotBlank(itemResult.getValue())) {
                            itemResult.setAbnormalFlag("1");
                            itemResult.setAbnormalFlagDesc("异常");
                            itemResult.setCriticalFlag(null);
                            itemResult.setCriticalDegree(null);
                            if (StringUtils.equals(itemResult.getValueType(), ExConstants.ITEM_VALUE_TYPE_说明型)) {
                                itemResult.setCheckConclusion(itemResult.getValue());
                            }
                            //itemResult.setAbnormalFlag(null);
                            itemResult.setAbnormalSymbol(null);
                        } else {
                            itemResult.setAbnormalFlag(null);
                            itemResult.setAbnormalFlagDesc(null);
                            itemResult.setCriticalFlag(null);
                            itemResult.setCriticalDegree(null);
                            itemResult.setCheckConclusion(null);
                            //itemResult.setAbnormalFlag(null);
                            itemResult.setAbnormalSymbol(null);
                        }
                    }
                }
            } else {
               /* itemResult.setAbnormalFlag(itemResult.getAbnormalFlagManual());
                itemResult.setAbnormalFlagDesc(itemResult.getAbnormalFlagDescManual());
                itemResult.setAbnormalFlagDescManual(itemResult.getAbnormalFlagDescManual());*/
            }

            if (StringUtils.equals(itemResult.getAbnormalFlag(), "1")) {
                abnormalGroupIdSet.add(itemResult.getItemGroupId());
            }
            saveOrUpdate(itemResult);
        }

        //saveOrUpdateBatch(customerRegItemResultList);
        String customerRegId = customerRegItemResultList.get(0).getCustomerRegId();
        //去重itemGroupId
        List<String> customerRegItemGroupIdList = customerRegItemResultList.stream().map(CustomerRegItemResult::getItemGroupId).distinct().collect(Collectors.toList());

        //  自动处理有部位和无部位的项目组
        if ((Objects.isNull(isTempSave) || !isTempSave) && !groupIdSet.isEmpty()) {
            smartUpdateItemGroupCheckStatus(customerRegId, customerRegItemGroupIdList, ExConstants.CHECK_STATUS_已检, null, sysUser.getUsername(), sysUser.getRealname());
        }

        // 自动处理有部位和无部位的项目组
        if ((Objects.isNull(isTempSave) || !isTempSave) && !abnormalGroupIdSet.isEmpty()) {
            smartUpdateItemGroupAbnormalFlag(customerRegId, new ArrayList<>(abnormalGroupIdSet), "1");
        }

        return customerRegItemResultList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CustomerRegItemResult saveItemResult(CustomerRegItemResult customerRegItemResult) {
        saveOrUpdate(customerRegItemResult);
        return customerRegItemResult;
    }

    @Override
    public CustomerRegItemResult abandonItemResult(CustomerRegItemResult customerRegItemResult) {

        if (StringUtils.isBlank(customerRegItemResult.getId())) {
            customerRegItemResult.setAbandonFlag(1);
            save(customerRegItemResult);
        } else {
            LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(CustomerRegItemResult::getId, customerRegItemResult.getId()).set(CustomerRegItemResult::getAbandonFlag, 1);
            customerRegItemResultMapper.update(null, lambdaUpdateWrapper);
        }


        boolean isAllItemAbandoned = isAllItemAbandoned(customerRegItemResult.getItemGroupId(), customerRegItemResult.getCustomerRegId());
        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegItemResult.getCustomerRegId()).eq(CustomerRegItemGroup::getItemGroupId, customerRegItemResult.getItemGroupId()).set(CustomerRegItemGroup::getAbandonFlag, isAllItemAbandoned ? 1 : 0);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);


        customerRegItemResult.setAbandonFlag(1);
        return customerRegItemResult;
    }

    @Override
    public void abandonGroupBatch(String customerRegId, List<String> itemGroupIds) {
        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).in(CustomerRegItemGroup::getItemGroupId, itemGroupIds).set(CustomerRegItemGroup::getAbandonFlag, 1);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);

        LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemResult::getCustomerRegId, customerRegId).in(CustomerRegItemResult::getItemGroupId, itemGroupIds).set(CustomerRegItemResult::getAbandonFlag, 1);
        customerRegItemResultMapper.update(null, lambdaUpdateWrapper);
    }

    @Override
    public void unAbandonGroupBatch(String customerRegId, List<String> itemGroupIds) {
        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).in(CustomerRegItemGroup::getItemGroupId, itemGroupIds).set(CustomerRegItemGroup::getAbandonFlag, 0);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);

        LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemResult::getCustomerRegId, customerRegId).in(CustomerRegItemResult::getItemGroupId, itemGroupIds).set(CustomerRegItemResult::getAbandonFlag, 0);
        customerRegItemResultMapper.update(null, lambdaUpdateWrapper);
    }


    @Override
    public CustomerRegItemResult unAbandonItemResult(CustomerRegItemResult customerRegItemResult) {
        LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemResult::getId, customerRegItemResult.getId()).set(CustomerRegItemResult::getAbandonFlag, 0);
        customerRegItemResultMapper.update(null, lambdaUpdateWrapper);

        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegItemResult.getCustomerRegId()).eq(CustomerRegItemGroup::getItemGroupId, customerRegItemResult.getItemGroupId()).set(CustomerRegItemGroup::getAbandonFlag, 0);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);

        customerRegItemResult.setAbandonFlag(0);
        return customerRegItemResult;
    }

    @Override
    public void abandonGroup(String itemGroupId, String customerRegId) {

        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).eq(CustomerRegItemGroup::getItemGroupId, itemGroupId).set(CustomerRegItemGroup::getAbandonFlag, 1);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);

        LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemResult::getItemGroupId, itemGroupId).eq(CustomerRegItemResult::getCustomerRegId, customerRegId).set(CustomerRegItemResult::getAbandonFlag, 1);
        customerRegItemResultMapper.update(null, lambdaUpdateWrapper);

    }

    @Override
    public void unAbandonGroup(String itemGroupId, String customerRegId) {
        LambdaUpdateWrapper<CustomerRegItemGroup> groupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        groupLambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).eq(CustomerRegItemGroup::getItemGroupId, itemGroupId).set(CustomerRegItemGroup::getAbandonFlag, 0);
        customerRegItemGroupMapper.update(null, groupLambdaUpdateWrapper);

        LambdaUpdateWrapper<CustomerRegItemResult> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemResult::getItemGroupId, itemGroupId).eq(CustomerRegItemResult::getCustomerRegId, customerRegId).set(CustomerRegItemResult::getAbandonFlag, 0);
        customerRegItemResultMapper.update(null, lambdaUpdateWrapper);
    }

    @Override
    public List<CustomerRegItemGroup> listGroupByRegId(String regId, List<String> departmentIds) {
        return listGroupByRegId(regId, departmentIds, false);
    }

    @Override
    public List<CustomerRegItemGroup> listGroupByRegId(String regId, List<String> departmentIds, boolean includeDependencies) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        /*String loginDeptCode = userInterfaceService.getAdminDeptCodeById(sysUser.getUsername());
        String deptCode = userInterfaceService.getAdminDeptCodeById(sysSettingService.getValueByCode("wx_operator_code"));
        Boolean containFeeOnly = false;
        if (!StringUtils.equals(loginDeptCode, deptCode)) {
            containFeeOnly = true;
        }*/
        long start = System.currentTimeMillis();
        List<CustomerRegItemGroup> groupList = customerRegItemResultMapper.listCustomerRegGroupByRegId(regId, departmentIds, true);
        long end = System.currentTimeMillis();
        System.out.println("科室工作站-查询大项耗时：" + (end - start));

        // 如果需要包含依赖信息，则查询并附加依赖数据
        if (includeDependencies && !groupList.isEmpty()) {
            attachDependencyInfo(groupList, regId);
        }

        //过滤掉已减项的和退款成功的记录
        groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.PAY_STATE_退款成功)).collect(Collectors.toList());

        if (!groupList.isEmpty()) {
            long start1 = System.currentTimeMillis();
            List<CustomerRegItemResult> allItemResultList = customerRegItemResultMapper.listItemResultByReg(regId);
            long end1 = System.currentTimeMillis();
            System.out.println("科室工作站-查询小项耗时：" + (end1 - start1));
            List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("checkStatus");
            groupList.forEach(itemGroup -> {
                List<ItemInfo> itemInfoList = customerRegItemResultMapper.listItemByCustomerGroup(itemGroup.getItemGroupId(), itemGroup.getCheckPartCode(), regId,itemGroup.getCheckStatus());
                itemInfoList.forEach(itemInfo -> {
                    //检查itemInfo下的itemResult的放弃状态，如果为放弃，则itemInfo的检查状态为放弃
                    String checkStatus = "";
                    if (itemInfo.getItemResult() != null) {
                        if (itemInfo.getItemResult().getAbandonFlag() == 1) {
                            checkStatus = ExConstants.CHECK_STATUS_放弃;
                        } else {
                            checkStatus = ExConstants.CHECK_STATUS_已检;
                        }
                    } else {
                        checkStatus = ExConstants.CHECK_STATUS_未检;
                    }

                    itemInfo.setCheckStatus(checkStatus);

                    //为每个小项查找默认值
                    if (StringUtils.isNotBlank(itemInfo.getId())) {
                        //ItemStandard normalStandard = itemStandardMapper.selectStandardByResultSymbo(itemInfo.getId(), ExConstants.ITEM_SYMBO_正常);
                        ItemDict itemDict = itemDictMapper.getDefaultDict(itemInfo.getId());

                        if (itemDict != null) {
                            itemInfo.setNormalDefaultValue(itemDict.getDictText());
                        }
                    }
                });

                //从allItemResultList中找到groupId和checkPartCode相同的，并且item_id 是null的记录
//                List<CustomerRegItemResult> nonItemIdResultList = allItemResultList.stream().filter(itemResult -> itemResult.getItemGroupId().equals(itemGroup.getItemGroupId()) && StringUtils.isBlank(itemResult.getItemId())).toList();
                List<CustomerRegItemResult> nonItemIdResultList = allItemResultList.stream().filter(itemResult -> {
                    // 基本条件：itemGroupId相等且itemId为空
                    boolean basicMatch = itemResult.getItemGroupId().equals(itemGroup.getItemGroupId()) && StringUtils.isBlank(itemResult.getItemId());

                    if (!basicMatch) {
                        return false;
                    }

                    // 检查checkPartCode匹配
                    String groupCheckPartCode = itemGroup.getCheckPartCode();
                    String resultCheckPartCode = itemResult.getCheckPartCode();

                    // 如果itemGroup的checkPartCode为空，则不需要匹配checkPartCode
                    if (StringUtils.isBlank(groupCheckPartCode)) {
                        return true;
                    }

                    // 如果itemGroup的checkPartCode不为空，则必须相等
                    return StringUtils.equals(groupCheckPartCode, resultCheckPartCode);
                }).toList();
                nonItemIdResultList.forEach(itemResult -> {
                    //从itemInfoList中找到his_code相同的记录
                    ItemInfo itemInfo = itemInfoList.stream().filter(info -> StringUtils.equals(info.getHisCode(), itemResult.getItemHisCode())).findFirst().orElse(null);
                    if(itemInfo == null){
                        itemInfo = new ItemInfo();
                        itemInfo.setName(itemResult.getItemName());
                        itemInfo.setItemResult(itemResult);
                        itemInfo.setCheckStatus(ExConstants.CHECK_STATUS_已检);
                        itemInfo.setNormalRef(itemResult.getValueRefRange());
                        itemInfo.setUnit(itemResult.getUnit());
                        itemInfo.setItemType("");
                        itemInfoList.add(itemInfo);
                    }else {
                        itemInfo.setItemResult(itemResult);
                        itemInfo.setCheckStatus(ExConstants.CHECK_STATUS_已检);
                    }
                });

                itemGroup.setItemList(itemInfoList);
                String valueToSearch = itemGroup.getCheckStatus();
                Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();
                if (optionalDictModel.isPresent()) {
                    String color = optionalDictModel.get().getColor();
                    itemGroup.setCheckStatusColor(color);
                }
            });
        }

        return groupList;
    }

    @Override
    public boolean isAllItemAbandoned(String itemGroupId, String customerRegId) {
        // Query to count total items in the group for the customer
        String totalItemsQuery = "SELECT COUNT(1) FROM customer_reg_item_group g join itemgroup_item ii on g.item_group_id=ii.group_id WHERE g.item_group_id=? AND g.customer_reg_id=?";
        // Query to count abandoned items in the group for the customer
        String abandonedItemsQuery = "SELECT COUNT(1) FROM customer_reg_item_result WHERE abandon_flag=1 AND item_group_id=? AND customer_reg_id=?";

        Integer totalItemsCount = jdbcTemplate.queryForObject(totalItemsQuery, Integer.class, itemGroupId, customerRegId);
        Integer abandonedItemsCount = jdbcTemplate.queryForObject(abandonedItemsQuery, Integer.class, itemGroupId, customerRegId);

        // Ensure counts are not null
        totalItemsCount = totalItemsCount != null ? totalItemsCount : 0;
        abandonedItemsCount = abandonedItemsCount != null ? abandonedItemsCount : 0;

        // If total items count equals abandoned items count, all items are considered abandoned
        return totalItemsCount.equals(abandonedItemsCount);
    }

    @Override
    public ItemStandard findMatchedItemStandard(CustomerRegItemResult itemResult, CustomerReg reg) {

        List<ItemStandard> itemStandardList = itemStandardMapper.selectByMainId(itemResult.getItemId());
        if (itemStandardList == null || itemStandardList.isEmpty()) {
            return null;
        }

        for (ItemStandard standard : itemStandardList) {
            if (standard.isApplicableTo(reg)) {

                String groovyExpression = standard.getGroovyExpression();
                if (StringUtils.isNotBlank(groovyExpression)) {
                    try {
                        boolean result = false;
                        result = GroovyUtil.getInstance().executeExpression(groovyExpression, itemResult.getValue());
                        if (result) {
                            return standard;
                        }
                    } catch (GroovyResultException e) {
                        log.error("查找匹配参考值，计算groovy表达式异常，itemStandard：" + JSONObject.toJSONString(standard) + "\r\n" + "itemResult:" + JSONObject.toJSONString(itemResult) + "\r\n" + "customerReg:" + JSONObject.toJSONString(reg), e);
                    }
                } else {
                    if (StringUtils.equals(itemResult.getValueType(), ExConstants.ITEM_VALUE_TYPE_数值型) || StringUtils.equals(itemResult.getValueType(), ExConstants.ITEM_VALUE_TYPE_计算型)) {
                        //数值型，判断是否在正常范围内
                        if (standard.isInRange(itemResult.getValue())) {
                            return standard;
                        }
                    } else if (StringUtils.equals(itemResult.getValueType(), ExConstants.ITEM_VALUE_TYPE_说明型)) {
                        //文本型
                        if (standard.isMatched(itemResult.getValue())) {
                            return standard;
                        }
                    }
                }
            }
        }

        return null;
    }

    @Override
    public List<CustomerRegItemResult> listByRegId(String regId) {
        return customerRegItemResultMapper.listByRegId(regId);
    }

    @Override
    public List<CustomerRegItemResult> listKeyResultByIdCard(String idCard, Integer year) {

        return customerRegItemResultMapper.listKeyHistoryResultByIdCard(idCard, year);
    }

    @Override
    public List<CustomerRegItemResult> listHistoryResultByIdCard(String idCard, Integer year) {
        return customerRegItemResultMapper.listHistoryResultByIdCard(idCard, year);
    }

    @Override
    public List<String> listHistoryAbnormaoResultIdsByIdCard(String idCard, Integer year) {
        return customerRegItemResultMapper.listHistoryAbnormaoResultIdsByIdCard(idCard, year);
    }


    @Override
    public void resetCheckStatusBatch(String customerRegId, List<String> itemGroupIds) {
        LambdaUpdateWrapper<CustomerRegItemGroup> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).in(CustomerRegItemGroup::getItemGroupId, itemGroupIds).set(CustomerRegItemGroup::getCheckStatus, ExConstants.CHECK_STATUS_未检).set(CustomerRegItemGroup::getCheckTime, null).set(CustomerRegItemGroup::getCheckDoctorCode, null).set(CustomerRegItemGroup::getCheckDoctorName, null).set(CustomerRegItemGroup::getCheckDoctorSignPic, null);
        customerRegItemGroupMapper.update(null, lambdaUpdateWrapper);
    }

    @Override
    public void setStatusCheckedtusBatch(String customerRegId, List<String> itemGroupIds) {
        LambdaUpdateWrapper<CustomerRegItemGroup> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemGroup::getCustomerRegId, customerRegId).in(CustomerRegItemGroup::getItemGroupId, itemGroupIds).set(CustomerRegItemGroup::getCheckStatus, ExConstants.CHECK_STATUS_已检);
        customerRegItemGroupMapper.update(null, lambdaUpdateWrapper);
    }

    @Override
    public boolean sendCheckNotifyBatch(String customerRegId, List<String> itemGroupNames) {
        //发送短信通知
        //拼接短信内容
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return false;
        }
        String phone = customerReg.getPhone();
        if (StringUtils.isBlank(phone)) {
            return false;
        }

        //查询租户名称
        String tenantName = "";
        try {
            tenantName = jdbcTemplate.queryForObject("SELECT name FROM sys_tenant WHERE del_flag='0' or del_flag is null limit 1", String.class);
        } catch (Exception ingore) {
        }
        tenantName = StringUtils.isNotBlank(tenantName) ? tenantName : "体检中心";

        String smsContent = "尊敬的" + customerReg.getName() + "，您好！请您补检以下项目：" + StringUtils.join(itemGroupNames) + "，谢谢！" + tenantName;
        smsRecordsService.saveAndSendSms(ExConstants.SMS_SEND_METHOD_IMMEDIATELY, "0", phone, smsContent, null, "补检通知");

        return true;
    }

    @Override
    public List<CustomerRegItemResult> listAll() {
        return customerRegItemResultMapper.listAll();
    }

    @Override
    public List<DepartAndGroupBean> listDepartAndGroup(String customerRegId) {

        //1、获取小项结果
        List<CustomerRegItemResult> resultList = listByRegId(customerRegId);
        resultList.forEach(itemResult -> {
            if (itemResult.getAbandonFlag() == 1) {
                itemResult.setCheckStatus(ExConstants.CHECK_STATUS_放弃);
            } else {
                itemResult.setCheckStatus(ExConstants.CHECK_STATUS_已检);
            }
        });
        //将resultList按照itemGroupId进行分组，如果checkPartCode不为空则按itemGroupId+checkPartCode分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(result -> {
            String groupKey = result.getItemGroupId();
            // 如果checkPartCode不为空，则将其加入分组键
            if (StringUtils.isNotBlank(result.getCheckPartCode())) {
                groupKey = groupKey + "-" + result.getCheckPartCode();
            }
            return groupKey;
        }));

        //2、设置按照科室分组的组合结果
        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listWithItemGroupByReg(customerRegId, null, false);
        //过滤掉已减项的和退款成功的记录
        groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)).toList();
        //为groupList设置itemResultList
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("checkStatus");
        groupList.forEach(group -> {
            String groupKey = group.getItemGroupId() +(StringUtils.isNotBlank(group.getCheckPartCode())?"-" +group.getCheckPartCode():"");
            List<CustomerRegItemResult> itemResultList = groupedByGroupIdResults.get(groupKey);
            group.setResultList(Objects.requireNonNullElseGet(itemResultList, ArrayList::new));
            String valueToSearch = group.getCheckStatus();
            Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();
            if (optionalDictModel.isPresent()) {
                String color = optionalDictModel.get().getColor();
                group.setCheckStatusColor(color);
            }
        });

        //对groupList按照departmentId进行分组
        Map<String, List<CustomerRegItemGroup>> groupedByDepartIdGroups = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getDepartmentId));

        //拼装departAndGroupBean的List
        List<DepartAndGroupBean> departAndGroupBeanList = groupedByDepartIdGroups.entrySet().stream().map(entry -> {
            DepartAndGroupBean departAndGroupBean = new DepartAndGroupBean();
            SysDepart depart = sysDepartMapper.selectById(entry.getKey());
            departAndGroupBean.setDepart(depart);
            departAndGroupBean.setGroupList(entry.getValue());
            return departAndGroupBean;
        }).filter(departAndGroupBean -> departAndGroupBean.getDepart() != null) // 过滤掉找不到科室的记录
        .sorted(Comparator.comparingInt(o -> {
            // 添加空值保护，虽然已经过滤了null，但为了安全起见还是保留检查
            if (o.getDepart().getSummarySort() == null) {
                return 0;
            }
            return o.getDepart().getSummarySort();
        })).collect(Collectors.toCollection(ArrayList::new));

        //获取科室小结
        List<CustomerRegDepartSummary> departSummaryList = customerRegDepartSummaryMapper.listByCustomerReg(customerRegId, null);
        //设置科室小结标志
        departAndGroupBeanList.forEach(departAndGroupBean -> {
            List<CustomerRegItemGroup> groupList1 = departAndGroupBean.getGroupList();
            boolean summaryFlag = groupList1.stream().allMatch(group -> StringUtils.equals(group.getCheckStatus(), ExConstants.CHECK_STATUS_已检));
            departAndGroupBean.setSummaryFlag(summaryFlag);
            //设置科室小结
            Optional<CustomerRegDepartSummary> optionalSummary = departSummaryList.stream().filter(summary -> StringUtils.equals(summary.getDepartmentId(), departAndGroupBean.getDepart().getId())).findFirst();
            optionalSummary.ifPresent(departAndGroupBean::setDepartSummary);
        });


        return departAndGroupBeanList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void clearGroupResult(String itemGroupId, String customerRegId, String departmentId) {
        if (StringUtils.isNotBlank(itemGroupId) && StringUtils.isNotBlank(customerRegId)) {
            jdbcTemplate.update("delete from customer_reg_item_result where item_group_id=? and customer_reg_id=?", itemGroupId, customerRegId);
            jdbcTemplate.update("update customer_reg_item_group set check_status=? where item_group_id=? and customer_reg_id=?", ExConstants.CHECK_STATUS_未检, itemGroupId, customerRegId);
            jdbcTemplate.update("delete from customer_reg_depart_summary  where department_id=? and customer_reg_id=?", departmentId, customerRegId);
        }
        //删除关联的危急值
        jdbcTemplate.update("delete from customer_reg_critical_item where item_group_id=? and customer_reg_id=?", itemGroupId, customerRegId);
    }

    @Override
    public List<StatusStat> statCheckStatusByRegId(String regId) {

        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listLiteByRegId(regId);
        //过滤掉已减项的、退款成功、已放弃、仅收费的项目
        groupList = groupList.stream().filter( group-> !StringUtils.equals(group.getChargeItemOnlyFlag(),"1")).toList();
        //将abandonFlag为1的checkStatus设置为放弃
        groupList.forEach(group -> {
            if (group.getAbandonFlag() != null && group.getAbandonFlag() == 1) {
                group.setCheckStatus(ExConstants.CHECK_STATUS_放弃);
            }
        });
        //按照checkStatus（已检、未检、放弃）进行分组，以便统计
        Map<String, List<CustomerRegItemGroup>> groupedByCheckStatusGroups = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCheckStatus));

        List<StatusStat> statusStatList = new ArrayList<>();
        groupedByCheckStatusGroups.forEach((checkStatus, groups) -> {
            StatusStat statusStat = new StatusStat();
            statusStat.setStatus(checkStatus);
            statusStat.setCount(groups.size());
            statusStat.setItems(groups.stream().map(CustomerRegItemGroup::getItemGroupName).collect(Collectors.toList()));
            statusStatList.add(statusStat);
        });
        //填充颜色
        List<DictModel> dictModelList = commonAPI.queryDictItemsByCode("checkStatus");
        statusStatList.forEach(statusStat -> {
            String valueToSearch = statusStat.getStatus();
            Optional<DictModel> optionalDictModel = dictModelList.stream().filter(dictModel -> valueToSearch.equals(dictModel.getValue())).findFirst();
            if (optionalDictModel.isPresent()) {
                String color = optionalDictModel.get().getColor();
                statusStat.setColor(color);
            }
        });


        return statusStatList;
    }

    @Override
    public List<CustomerRegItemGroup> listUncheckGroupByRegId(String regId) {
        LambdaQueryWrapper<CustomerRegItemGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerRegItemGroup::getCustomerRegId, regId).eq(CustomerRegItemGroup::getCheckStatus, ExConstants.CHECK_STATUS_未检);
        return customerRegItemGroupMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<CustomerRegItemGroup> getAbnormalItemGroupList(String customerRegId) {
        //1、获取登记记录的所有小项结果
        List<CustomerRegItemResult> resultList = customerRegItemResultMapper.getItemResultByReg(customerRegId);

        //2、获取需要保留在最终结果中的小项
        List<CustomerRegItemResult> finalResultList = new ArrayList<>();
        resultList.forEach(itemResult -> {
            String abnormalFlag = itemResult.getAbnormalFlag();
            Integer sumableNormalVal = itemResult.getSumableNormalvalFlag();
            sumableNormalVal = sumableNormalVal == null ? 0 : sumableNormalVal;
            Integer sumableAbnormalVal = itemResult.getSumableFlag();
            sumableAbnormalVal = sumableAbnormalVal == null ? 1 : sumableAbnormalVal;

            if (StringUtils.equals(abnormalFlag, "1") && sumableAbnormalVal == 1) {
                finalResultList.add(itemResult);
            } else if (StringUtils.equals(abnormalFlag, "0") && sumableNormalVal == 1) {
                finalResultList.add(itemResult);
            }
        });

        //3、将小项集合（finalResultList）按照itemGroupId进行分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = finalResultList.stream().collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));

        //4、获取登记记录的所有大项
        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listLiteByRegId(customerRegId);

        //5、给大项设置小项集合
        groupList.forEach(group -> {
            List<CustomerRegItemResult> itemResultList = groupedByGroupIdResults.get(group.getItemGroupId());
            group.setResultList(itemResultList);
        });

        //6、获取异常的大项集合：过滤abnormalFlag等于1的大项并且itemResutlList不为空

        return new ArrayList<>(groupList.stream().filter(group -> group.getResultList() != null && !group.getResultList().isEmpty()).toList());
    }

    /**
     * 为项目组列表附加依赖信息（优化版本）
     * @param groupList 项目组列表
     * @param regId 登记ID
     */
    private void attachDependencyInfo(List<CustomerRegItemGroup> groupList, String regId) {
        try {
            long start = System.currentTimeMillis();

            if (groupList.isEmpty()) {
                return;
            }

            // 1. 预构建索引映射，避免重复遍历
            DependencyIndexes indexes = buildDependencyIndexes(groupList, regId);

            // 2. 批量查询依赖关系（只查询存在的项目组）
            List<ItemGroupRelation> allDependencies = itemGroupRelationService.list(
                new LambdaQueryWrapper<ItemGroupRelation>()
                    .in(ItemGroupRelation::getGroupId, indexes.groupIds)
                    .eq(ItemGroupRelation::getRelation, "依赖")
            );

            if (allDependencies.isEmpty()) {
                System.out.println("没有找到依赖关系，跳过依赖信息附加");
                return;
            }

            // 3. 按groupId分组依赖关系（一次性分组）
            Map<String, List<ItemGroupRelation>> dependencyMap = allDependencies.stream()
                .collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));

            // 4. 利用预构建索引快速构建依赖项目结果
            Map<String, List<DependentItemResultDTO>> dependentResultsMap =
                buildDependentResultsWithIndexes(indexes, dependencyMap);

            // 5. 为每个项目组设置依赖信息（利用索引直接访问）
            dependencyMap.forEach((groupId, dependencies) -> {
                CustomerRegItemGroup group = indexes.groupMap.get(groupId);
                if (group != null) {
                    group.setDependencies(dependencies);
                    group.setDependentResults(dependentResultsMap.get(groupId));
                }
            });

            long end = System.currentTimeMillis();
            System.out.println("附加依赖信息耗时：" + (end - start) + "ms，处理了 " +
                dependencyMap.size() + " 个有依赖关系的项目组");

        } catch (Exception e) {
            System.err.println("附加依赖信息失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 依赖项目索引结构，预构建所有必要的映射关系
     */
    private static class DependencyIndexes {
        // 基础索引
        final List<String> groupIds;
        final Map<String, CustomerRegItemGroup> groupMap;
        final Map<String, CustomerRegItemGroup> groupByRegGroupId;

        // 结果索引
        final Map<String, List<CustomerRegItemResult>> resultsByGroupId;
        final Map<String, CustomerRegItemResult> resultsByItemId;

        // 项目信息索引
        final Map<String, List<ItemInfo>> itemsByGroupId;
        final Map<String, ItemInfo> itemsById;

        DependencyIndexes(List<String> groupIds,
                         Map<String, CustomerRegItemGroup> groupMap,
                         Map<String, CustomerRegItemGroup> groupByRegGroupId,
                         Map<String, List<CustomerRegItemResult>> resultsByGroupId,
                         Map<String, CustomerRegItemResult> resultsByItemId,
                         Map<String, List<ItemInfo>> itemsByGroupId,
                         Map<String, ItemInfo> itemsById) {
            this.groupIds = groupIds;
            this.groupMap = groupMap;
            this.groupByRegGroupId = groupByRegGroupId;
            this.resultsByGroupId = resultsByGroupId;
            this.resultsByItemId = resultsByItemId;
            this.itemsByGroupId = itemsByGroupId;
            this.itemsById = itemsById;
        }
    }

    /**
     * 构建依赖项目索引（一次性构建所有必要的映射关系）
     * @param groupList 项目组列表
     * @param regId 登记ID
     * @return 依赖项目索引
     */
    private DependencyIndexes buildDependencyIndexes(List<CustomerRegItemGroup> groupList, String regId) {
        long indexStart = System.currentTimeMillis();

        // 1. 构建项目组索引
        List<String> groupIds = new ArrayList<>(groupList.size());
        Map<String, CustomerRegItemGroup> groupMap = new HashMap<>(groupList.size());
        Map<String, CustomerRegItemGroup> groupByRegGroupId = new HashMap<>(groupList.size());
        Map<String, List<ItemInfo>> itemsByGroupId = new HashMap<>(groupList.size());
        Map<String, ItemInfo> itemsById = new HashMap<>();

        for (CustomerRegItemGroup group : groupList) {
            groupIds.add(group.getItemGroupId());
            groupMap.put(group.getItemGroupId(), group);
            groupByRegGroupId.put(group.getId(), group);

            // 构建小项索引
            if (group.getItemList() != null) {
                itemsByGroupId.put(group.getItemGroupId(), group.getItemList());
                for (ItemInfo item : group.getItemList()) {
                    itemsById.put(item.getId(), item);
                }
            }
        }

        // 2. 一次性查询所有小项结果并构建索引
        List<CustomerRegItemResult> allResults = listByRegId(regId);
        Map<String, List<CustomerRegItemResult>> resultsByGroupId = allResults.stream()
            .collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));
        Map<String, CustomerRegItemResult> resultsByItemId = allResults.stream()
            .filter(r -> r.getItemId() != null)
            .collect(Collectors.toMap(CustomerRegItemResult::getItemId, r -> r, (r1, r2) -> r1));

        long indexEnd = System.currentTimeMillis();
        System.out.println("构建依赖索引耗时：" + (indexEnd - indexStart) + "ms，索引了 " +
            groupList.size() + " 个项目组，" + allResults.size() + " 个小项结果");

        return new DependencyIndexes(groupIds, groupMap, groupByRegGroupId,
            resultsByGroupId, resultsByItemId, itemsByGroupId, itemsById);
    }

    /**
     * 利用预构建索引快速构建依赖项目结果
     * @param indexes 预构建的索引
     * @param dependencyMap 依赖关系映射
     * @return 依赖项目结果映射
     */
    private Map<String, List<DependentItemResultDTO>> buildDependentResultsWithIndexes(
            DependencyIndexes indexes, Map<String, List<ItemGroupRelation>> dependencyMap) {

        Map<String, List<DependentItemResultDTO>> resultMap = new HashMap<>(dependencyMap.size());

        // 为每个主项目组构建依赖项目结果
        dependencyMap.forEach((mainGroupId, dependencies) -> {
            List<DependentItemResultDTO> dependentResults = new ArrayList<>(dependencies.size());

            for (ItemGroupRelation dependency : dependencies) {
                DependentItemResultDTO dto = null;

                if ("GROUP".equals(dependency.getRelationItemType())) {
                    // 大项依赖 - 直接从索引获取
                    CustomerRegItemGroup dependentGroup = indexes.groupMap.get(dependency.getRelationGroupId());
                    if (dependentGroup != null) {
                        dto = buildGroupResultDTOWithIndex(dependentGroup, indexes);
                    }
                } else if ("ITEM".equals(dependency.getRelationItemType())) {
                    // 小项依赖 - 直接从索引获取
                    dto = buildItemResultDTOWithIndex(dependency, indexes);
                }

                if (dto != null) {
                    dependentResults.add(dto);
                }
            }

            if (!dependentResults.isEmpty()) {
                resultMap.put(mainGroupId, dependentResults);
            }
        });

        return resultMap;
    }

    /**
     * 利用索引构建大项结果DTO（优化版本）
     */
    private DependentItemResultDTO buildGroupResultDTOWithIndex(CustomerRegItemGroup group,
                                                               DependencyIndexes indexes) {
        List<CustomerRegItemResult> groupResults = indexes.resultsByGroupId.get(group.getItemGroupId());
        if (groupResults == null || groupResults.isEmpty()) {
            return null;
        }

        DependentItemResultDTO dto = new DependentItemResultDTO();
        dto.setItemType("GROUP");
        dto.setItemId(group.getItemGroupId());
        dto.setItemName(group.getItemGroupName());
        dto.setCustomerRegItemGroupId(group.getId());

        // 优化：使用预计算的结果构建综合值
        String groupValue = buildGroupValueOptimized(groupResults);
        dto.setValue(groupValue);

        // 优化：使用Stream的短路特性快速判断异常
        boolean hasAbnormal = groupResults.stream()
            .anyMatch(r -> "1".equals(r.getAbnormalFlag()));
        dto.setAbnormalFlag(hasAbnormal ? "1" : "0");

        return dto;
    }

    /**
     * 利用索引构建小项结果DTO（优化版本）
     */
    private DependentItemResultDTO buildItemResultDTOWithIndex(ItemGroupRelation dependency,
                                                              DependencyIndexes indexes) {
        // 直接从索引获取，避免遍历
        CustomerRegItemResult itemResult = indexes.resultsByItemId.get(dependency.getRelationItemId());

        if (itemResult == null || itemResult.getValue() == null || itemResult.getValue().trim().isEmpty()) {
            return null;
        }

        DependentItemResultDTO dto = new DependentItemResultDTO();
        dto.setItemType("ITEM");
        dto.setItemId(itemResult.getItemId());
        dto.setItemName(itemResult.getItemName());
        dto.setCustomerRegItemGroupId(itemResult.getItemGroupId());
        dto.setValue(itemResult.getValue());
        dto.setUnit(itemResult.getUnit());
        dto.setAbnormalFlag(itemResult.getAbnormalFlag());
        dto.setValueRefRange(itemResult.getValueRefRange());

        return dto;
    }

    /**
     * 构建大项结果DTO（兼容旧版本）
     */
    private DependentItemResultDTO buildGroupResultDTO(CustomerRegItemGroup group,
                                                      Map<String, List<CustomerRegItemResult>> resultsByGroupId) {
        List<CustomerRegItemResult> groupResults = resultsByGroupId.get(group.getItemGroupId());
        if (groupResults == null || groupResults.isEmpty()) {
            return null;
        }

        DependentItemResultDTO dto = new DependentItemResultDTO();
        dto.setItemType("GROUP");
        dto.setItemId(group.getItemGroupId());
        dto.setItemName(group.getItemGroupName());
        dto.setCustomerRegItemGroupId(group.getId());

        String groupValue = buildGroupValue(groupResults);
        dto.setValue(groupValue);

        boolean hasAbnormal = groupResults.stream().anyMatch(r -> "1".equals(r.getAbnormalFlag()));
        dto.setAbnormalFlag(hasAbnormal ? "1" : "0");

        return dto;
    }

    /**
     * 构建小项结果DTO（兼容旧版本）
     */
    private DependentItemResultDTO buildItemResultDTO(ItemGroupRelation dependency,
                                                     List<CustomerRegItemResult> allResults) {
        CustomerRegItemResult itemResult = allResults.stream()
            .filter(r -> dependency.getRelationItemId().equals(r.getItemId()))
            .findFirst()
            .orElse(null);

        if (itemResult == null || itemResult.getValue() == null || itemResult.getValue().trim().isEmpty()) {
            return null;
        }

        DependentItemResultDTO dto = new DependentItemResultDTO();
        dto.setItemType("ITEM");
        dto.setItemId(itemResult.getItemId());
        dto.setItemName(itemResult.getItemName());
        dto.setCustomerRegItemGroupId(itemResult.getItemGroupId());
        dto.setValue(itemResult.getValue());
        dto.setUnit(itemResult.getUnit());
        dto.setAbnormalFlag(itemResult.getAbnormalFlag());
        dto.setValueRefRange(itemResult.getValueRefRange());

        return dto;
    }

    /**
     * 构建大项的综合值（优化版本）
     */
    private String buildGroupValueOptimized(List<CustomerRegItemResult> groupResults) {
        if (groupResults.isEmpty()) {
            return "";
        }

        // 使用StringBuilder避免字符串拼接开销
        StringBuilder valueBuilder = new StringBuilder();
        boolean first = true;

        for (CustomerRegItemResult result : groupResults) {
            if (result.getValue() != null && !result.getValue().trim().isEmpty()) {
                if (!first) {
                    valueBuilder.append("; ");
                }
                valueBuilder.append(result.getItemName()).append(":").append(result.getValue());
                first = false;
            }
        }

        return valueBuilder.toString();
    }

    /**
     * 构建大项的综合值（兼容旧版本）
     */
    private String buildGroupValue(List<CustomerRegItemResult> groupResults) {
        // 优先返回有值的结果，多个结果用分号分隔
        List<String> values = groupResults.stream()
            .filter(r -> r.getValue() != null && !r.getValue().trim().isEmpty())
            .map(r -> r.getItemName() + ":" + r.getValue())
            .collect(Collectors.toList());

        return values.isEmpty() ? "" : String.join("; ", values);
    }

    /**
     * 智能更新项目组检查状态
     * 自动判断项目组是否有部位设置，有部位的按部位更新，无部位的按原有逻辑更新
     *
     * @param regId 登记ID
     * @param itemGroupIds 项目组ID列表
     * @param status 检查状态
     * @param statusLimit 状态限制条件
     * @param checkDoctorCode 检查医生代码
     * @param checkDoctorName 检查医生姓名
     */
    private void smartUpdateItemGroupCheckStatus(String regId,
                                               List<String> itemGroupIds,
                                               String status,
                                               String statusLimit,
                                               String checkDoctorCode,
                                               String checkDoctorName) {
        if (itemGroupIds == null || itemGroupIds.isEmpty()) {
            return;
        }

        // 查询这些项目组的部位信息
        LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegItemGroup::getCustomerRegId, regId)
                   .in(CustomerRegItemGroup::getItemGroupId, itemGroupIds);
        List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.selectList(queryWrapper);

        // 分组：有部位的和无部位的
        List<String> withPartGroupIds = new ArrayList<>();
        List<String> withPartCodes = new ArrayList<>();
        List<String> withoutPartGroupIds = new ArrayList<>();

        for (CustomerRegItemGroup group : itemGroups) {
            if (StringUtils.isNotBlank(group.getCheckPartCode())) {
                // 有部位的项目组
                withPartGroupIds.add(group.getItemGroupId());
                withPartCodes.add(group.getCheckPartCode());
            } else {
                // 无部位的项目组
                withoutPartGroupIds.add(group.getItemGroupId());
            }
        }

        // 分别更新
        if (!withPartGroupIds.isEmpty()) {
            customerRegItemGroupMapper.updateItemGroupCheckStatusBatchWithPart(
                regId, withPartGroupIds, withPartCodes, status, statusLimit, checkDoctorCode, checkDoctorName);
        }

        if (!withoutPartGroupIds.isEmpty()) {
            customerRegItemGroupMapper.updateItemGroupCheckStatusBatch(
                regId, withoutPartGroupIds, status, statusLimit, checkDoctorCode, checkDoctorName);
        }
    }

    /**
     * 智能更新项目组异常标志
     * 自动判断项目组是否有部位设置，有部位的按部位更新，无部位的按原有逻辑更新
     *
     * @param regId 登记ID
     * @param itemGroupIds 项目组ID列表
     * @param abnormalFlag 异常标志
     */
    private void smartUpdateItemGroupAbnormalFlag(String regId,
                                                List<String> itemGroupIds,
                                                String abnormalFlag) {
        if (itemGroupIds == null || itemGroupIds.isEmpty()) {
            return;
        }

        // 查询这些项目组的部位信息
        LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegItemGroup::getCustomerRegId, regId)
                   .in(CustomerRegItemGroup::getItemGroupId, itemGroupIds);
        List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.selectList(queryWrapper);

        // 分组：有部位的和无部位的
        List<String> withPartGroupIds = new ArrayList<>();
        List<String> withPartCodes = new ArrayList<>();
        List<String> withoutPartGroupIds = new ArrayList<>();

        for (CustomerRegItemGroup group : itemGroups) {
            if (StringUtils.isNotBlank(group.getCheckPartCode())) {
                // 有部位的项目组
                withPartGroupIds.add(group.getItemGroupId());
                withPartCodes.add(group.getCheckPartCode());
            } else {
                // 无部位的项目组
                withoutPartGroupIds.add(group.getItemGroupId());
            }
        }

        // 分别更新
        if (!withPartGroupIds.isEmpty()) {
            customerRegItemGroupMapper.updateItemGroupAbnormalFlagBatchWithPart(
                regId, withPartGroupIds, withPartCodes, abnormalFlag);
        }

        if (!withoutPartGroupIds.isEmpty()) {
            customerRegItemGroupMapper.updateItemGroupAbnormalFlagBatch(regId, withoutPartGroupIds, abnormalFlag);
        }
    }


}
